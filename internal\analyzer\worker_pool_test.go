package analyzer

import (
	"sync"
	"sync/atomic"
	"testing"
)

func TestNewWorkerPool(t *testing.T) {
	pool := NewWorkerPool(4)
	if pool == nil {
		t.Fatal("Expected non-nil worker pool")
	}
	// Can't access private fields, so just test that pool was created successfully
}

func TestNewWorkerPool_ZeroWorkers(t *testing.T) {
	pool := NewWorkerPool(0)
	if pool == nil {
		t.Error("Expected non-nil WorkerPool")
	}
	// Should default to runtime.NumCPU() when workers <= 0
	// We can't access private field, so just test that pool was created
}

func TestWorkerPool_SubmitAndWait(t *testing.T) {
	pool := NewWorkerPool(2)
	pool.Start()
	defer pool.Close()

	// Test submitting jobs and waiting for completion
	var counter int
	var mu sync.Mutex

	for i := 0; i < 5; i++ {
		pool.Submit(func() {
			mu.Lock()
			counter++
			mu.Unlock()
		})
	}

	pool.Wait()

	if counter != 5 {
		t.<PERSON><PERSON><PERSON>("Expected counter to be 5, got %d", counter)
	}
}

func TestWorkerPool_ConcurrentJobs(t *testing.T) {
	pool := NewWorkerPool(3)
	pool.Start()
	defer pool.Close()

	// Test concurrent job execution
	var results []int
	var mu sync.Mutex

	for i := 0; i < 10; i++ {
		value := i
		pool.Submit(func() {
			// Simulate some work
			processedValue := value * 2
			mu.Lock()
			results = append(results, processedValue)
			mu.Unlock()
		})
	}

	pool.Wait()

	if len(results) != 10 {
		t.Errorf("Expected 10 results, got %d", len(results))
	}
}

func TestWorkerPool_StartOnce(t *testing.T) {
	pool := NewWorkerPool(2)
	
	// Start should be idempotent
	pool.Start()
	pool.Start() // Should not panic or create duplicate workers
	
	defer pool.Close()

	// Test that pool still works after multiple Start calls
	var executed bool
	pool.Submit(func() {
		executed = true
	})

	pool.Wait()

	if !executed {
		t.Error("Expected job to be executed")
	}
}

func TestWorkerPool_CloseAndResubmit(t *testing.T) {
	pool := NewWorkerPool(2)
	pool.Start()

	// Submit a job
	var executed bool
	pool.Submit(func() {
		executed = true
	})

	pool.Wait()
	pool.Close()

	if !executed {
		t.Error("Expected job to be executed before close")
	}
}

func TestWorkerPool_StressTest(t *testing.T) {
	pool := NewWorkerPool(4)
	pool.Start()
	defer pool.Close()

	// Submit many jobs to test pool capacity
	const numJobs = 100
	var completed int32

	for i := 0; i < numJobs; i++ {
		pool.Submit(func() {
			atomic.AddInt32(&completed, 1)
		})
	}

	pool.Wait()

	if int(completed) != numJobs {
		t.Errorf("Expected %d completed jobs, got %d", numJobs, completed)
	}
}

// Removed TestWorkerPool_ProcessImages_Concurrency as ProcessImages method doesn't exist

// Removed TestWorkerPool_ProcessImages_DifferentSizes as ProcessImages method doesn't exist

// Removed TestWorkerPool_ProcessImages_StressTest as ProcessImages method doesn't exist

// Removed TestWorkerPool_ProcessImages_RaceCondition as ProcessImages method doesn't exist

// Removed TestWorkerPool_ProcessImages_SingleWorker as ProcessImages method doesn't exist